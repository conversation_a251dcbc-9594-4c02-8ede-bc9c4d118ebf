import { OpenAPIHono } from '@hono/zod-openapi';

import { routes as circleRoutes } from '../circle/adminRoutes';
import { routes as eventRoutes } from '../event/adminRoutes';
import { routes as imageRoutes } from '../images/adminRoutes';
import { routes as logRoutes } from '../log/adminRoutes';
import { routes as statsRoutes } from '../stats/adminRoutes';
import { routes as userRoutes } from '../user/adminRoutes';
import { adminVenues } from '../venue/adminRoutes';
import { betterAuthMiddleware } from '@/middlewares/betterAuth';
import { roleGuard } from '@/middlewares/roleGuard';
import { HonoApp } from '@/types';

const routes = new OpenAPIHono<HonoApp>();

routes.use('*', betterAuthMiddleware());

/**
 * 角色权限控制策略：
 *
 * admin: 拥有所有权限
 * - 可以管理用户、查看日志、查看统计数据
 * - 可以管理活动、社团、场地、图片
 *
 * editor: 拥有除用户管理外的所有权限
 * - 可以管理活动、社团、场地、图片
 * - 不能访问用户列表、日志、统计数据
 *
 * user/viewer: 无管理权限
 */

// 编辑者权限：admin + editor 都可以访问
routes.use('/events/*', roleGuard(['admin', 'editor']));
routes.use('/circles/*', roleGuard(['admin', 'editor']));
routes.use('/venues/*', roleGuard(['admin', 'editor']));
routes.use('/images/*', roleGuard(['admin', 'editor']));

// 管理员专属权限：只有 admin 可以访问
routes.use('/users/*', roleGuard('admin')); // 用户管理
routes.use('/logs/*', roleGuard('admin')); // 系统日志
routes.use('/stats/*', roleGuard('admin')); // 统计数据

routes.route('/events', eventRoutes);
routes.route('/circles', circleRoutes);
routes.route('/venues', adminVenues);
routes.route('/images', imageRoutes);
routes.route('/users', userRoutes);
routes.route('/logs', logRoutes);
routes.route('/stats', statsRoutes);

export { routes };
